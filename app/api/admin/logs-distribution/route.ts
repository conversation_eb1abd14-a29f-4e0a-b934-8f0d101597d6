import { NextRequest, NextResponse } from 'next/server';
import { authManager } from '@/lib/auth/auth';
import { apiLogger } from '@/lib/utils/logger';
import { logsErrorTracker } from '@/lib/telegram/logs-error-tracker';

// GET /api/admin/logs-distribution - Get logs distribution statistics (admin only)
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  apiLogger.info('GET /api/admin/logs-distribution - Fetching logs distribution stats');
  
  try {
    // Check authentication and admin permissions
    const currentUser = await authManager.getCurrentUser();
    if (!currentUser) {
      apiLogger.warn('Unauthorized access attempt to logs distribution stats');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!currentUser.is_admin) {
      apiLogger.warn(`Non-admin user ${currentUser.id} attempted to access logs distribution stats`);
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const daysBack = parseInt(searchParams.get('days') || '7');

    // Get error statistics
    const errorStats = await logsErrorTracker.getErrorStats(daysBack);
    
    // Get problematic topics
    const problematicTopics = await logsErrorTracker.getProblematicTopics(daysBack);
    
    // Get recent errors for detailed view
    const recentErrors = await logsErrorTracker.getRecentErrors(20);

    // Calculate summary stats
    const totalErrors = errorStats.reduce((sum, stat) => sum + stat.error_count, 0);
    const totalAffectedTopics = new Set(
      problematicTopics.map(topic => `${topic.group_id}:${topic.topic_id || 'main'}`)
    ).size;

    const duration = Date.now() - startTime;
    apiLogger.logApiRequest('GET', '/api/admin/logs-distribution', currentUser.id, 200, duration);

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          total_errors: totalErrors,
          affected_topics: totalAffectedTopics,
          days_analyzed: daysBack,
          error_types: errorStats.length
        },
        error_stats: errorStats,
        problematic_topics: problematicTopics,
        recent_errors: recentErrors.map(error => ({
          id: error.id,
          tweet_url: error.tweet_url,
          headline: error.headline,
          group_title: error.group_title,
          topic_title: error.topic_title,
          error_type: error.error_type,
          error_message: error.error_message,
          retry_count: error.retry_count,
          created_at: error.created_at
        }))
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    apiLogger.error('Unexpected error in GET /api/admin/logs-distribution', error as Error);
    apiLogger.logApiRequest('GET', '/api/admin/logs-distribution', undefined, 500, duration);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/logs-distribution - Mark errors as resolved (admin only)
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  apiLogger.info('POST /api/admin/logs-distribution - Marking errors as resolved');
  
  try {
    // Check authentication and admin permissions
    const currentUser = await authManager.getCurrentUser();
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!currentUser.is_admin) {
      apiLogger.warn(`Non-admin user ${currentUser.id} attempted to mark logs errors as resolved`);
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { group_id, topic_id } = body;

    if (!group_id) {
      return NextResponse.json(
        { error: 'group_id is required' },
        { status: 400 }
      );
    }

    // Mark errors as resolved for the specified topic
    await logsErrorTracker.markTopicErrorsResolved(group_id, topic_id);

    const duration = Date.now() - startTime;
    apiLogger.info(`Marked logs errors as resolved for group ${group_id}/${topic_id || 'main'}`, {
      user_id: currentUser.id,
      group_id,
      topic_id
    });
    apiLogger.logApiRequest('POST', '/api/admin/logs-distribution', currentUser.id, 200, duration);

    return NextResponse.json({
      success: true,
      message: `Errors marked as resolved for group ${group_id}/${topic_id || 'main'}`
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    apiLogger.error('Unexpected error in POST /api/admin/logs-distribution', error as Error);
    apiLogger.logApiRequest('POST', '/api/admin/logs-distribution', undefined, 500, duration);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}