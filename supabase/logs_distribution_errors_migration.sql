-- Migration: Add logs distribution error tracking
-- This enables monitoring and debugging of logs distribution failures

-- Create table for tracking logs distribution errors
CREATE TABLE IF NOT EXISTS buddyintels_logs_distribution_errors (
  id INTEGER PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  tweet_url TEXT NOT NULL,
  headline TEXT NOT NULL,
  logs_message TEXT NOT NULL,
  group_id BIGINT NOT NULL,
  topic_id BIGINT,
  group_title TEXT,
  topic_title TEXT,
  error_type TEXT NOT NULL,
  error_message TEXT NOT NULL,
  error_details JSONB,
  retry_count INTEGER DEFAULT 0,
  resolved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_logs_distribution_errors_created_at 
ON buddyintels_logs_distribution_errors(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_logs_distribution_errors_group_topic 
ON buddyintels_logs_distribution_errors(group_id, topic_id);

CREATE INDEX IF NOT EXISTS idx_logs_distribution_errors_error_type 
ON buddyintels_logs_distribution_errors(error_type);

CREATE INDEX IF NOT EXISTS idx_logs_distribution_errors_resolved 
ON buddyintels_logs_distribution_errors(resolved) WHERE resolved = FALSE;

-- Add comment to document the feature
COMMENT ON TABLE buddyintels_logs_distribution_errors IS 'Tracks failed attempts to distribute logs messages to logs topics for debugging and monitoring';

-- Create a function to get logs distribution error statistics
CREATE OR REPLACE FUNCTION get_logs_distribution_stats(days_back INTEGER DEFAULT 7)
RETURNS TABLE (
  error_type TEXT,
  error_count BIGINT,
  affected_topics BIGINT,
  latest_occurrence TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    lde.error_type,
    COUNT(*) as error_count,
    COUNT(DISTINCT CONCAT(lde.group_id, ':', COALESCE(lde.topic_id::TEXT, 'main'))) as affected_topics,
    MAX(lde.created_at) as latest_occurrence
  FROM buddyintels_logs_distribution_errors lde
  WHERE lde.created_at >= (CURRENT_TIMESTAMP - INTERVAL '1 day' * days_back)
    AND lde.resolved = FALSE
  GROUP BY lde.error_type
  ORDER BY error_count DESC;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get problematic logs topics
CREATE OR REPLACE FUNCTION get_problematic_logs_topics(days_back INTEGER DEFAULT 7)
RETURNS TABLE (
  group_id BIGINT,
  topic_id BIGINT,
  group_title TEXT,
  topic_title TEXT,
  error_count BIGINT,
  latest_error TIMESTAMP WITH TIME ZONE,
  most_common_error TEXT
) AS $$
BEGIN
  RETURN QUERY
  WITH topic_errors AS (
    SELECT
      lde.group_id,
      lde.topic_id,
      MAX(lde.group_title) as group_title,
      MAX(lde.topic_title) as topic_title,
      COUNT(*) as error_count,
      MAX(lde.created_at) as latest_error,
      MODE() WITHIN GROUP (ORDER BY lde.error_type) as most_common_error
    FROM buddyintels_logs_distribution_errors lde
    WHERE lde.created_at >= (CURRENT_TIMESTAMP - INTERVAL '1 day' * days_back)
      AND lde.resolved = FALSE
    GROUP BY lde.group_id, lde.topic_id
  )
  SELECT
    te.group_id,
    te.topic_id,
    te.group_title,
    te.topic_title,
    te.error_count,
    te.latest_error,
    te.most_common_error
  FROM topic_errors te
  ORDER BY te.error_count DESC, te.latest_error DESC;
END;
$$ LANGUAGE plpgsql;