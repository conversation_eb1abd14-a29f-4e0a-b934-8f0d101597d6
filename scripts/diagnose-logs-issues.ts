#!/usr/bin/env bun

/**
 * Comprehensive diagnostic tool for logs distribution issues
 * Helps identify why logs messages might not be reaching topics
 */

import { topicManager } from '../lib/telegram/topic-manager';
import { aiSummarizer } from '../lib/ai/summarizer';
import { logsErrorTracker } from '../lib/telegram/logs-error-tracker';
import { Bot } from 'grammy';

async function diagnoseLogs() {
  console.log('🔍 BuddyIntels Logs Distribution Diagnostic Tool');
  console.log('=' .repeat(50));
  console.log();

  try {
    // Step 1: Check for logs topics in database
    console.log('📊 Step 1: Database Check');
    console.log('-'.repeat(30));
    
    const logsTopics = await topicManager.getLogsTopics();
    console.log(`Found ${logsTopics.length} active logs topics in database:`);
    
    if (logsTopics.length === 0) {
      console.error('❌ ISSUE: No active logs topics found!');
      console.log('   → Check if any topics are configured with type "logs"');
      console.log('   → Use /settype logs command in telegram to configure topics');
      return;
    }
    
    logsTopics.forEach((topic, index) => {
      console.log(`   ${index + 1}. ${topic.group_title}${topic.topic_title ? ` / ${topic.topic_title}` : ''} (ID: ${topic.group_id}/${topic.topic_id || 'main'})`);
    });
    console.log();

    // Step 2: Check recent errors
    console.log('🚨 Step 2: Recent Errors Analysis');
    console.log('-'.repeat(30));
    
    const errorStats = await logsErrorTracker.getErrorStats(7);
    
    if (errorStats.length === 0) {
      console.log('✅ No logs distribution errors in the past 7 days');
    } else {
      console.log('Recent errors (past 7 days):');
      errorStats.forEach(stat => {
        console.log(`   ${stat.error_type}: ${stat.error_count} errors affecting ${stat.affected_topics} topics`);
        console.log(`   Last occurrence: ${stat.latest_occurrence}`);
      });
    }
    
    const problematicTopics = await logsErrorTracker.getProblematicTopics(7);
    if (problematicTopics.length > 0) {
      console.log('\nProblematic topics:');
      problematicTopics.forEach(topic => {
        console.log(`   ${topic.group_title}${topic.topic_title ? ` / ${topic.topic_title}` : ''}: ${topic.error_count} errors`);
        console.log(`   Most common error: ${topic.most_common_error}`);
      });
    }
    console.log();

    // Step 3: Test bot connection (if token available)
    console.log('🤖 Step 3: Bot Connection Test');
    console.log('-'.repeat(30));
    
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    if (!botToken) {
      console.warn('⚠️  TELEGRAM_BOT_TOKEN not found - skipping bot connectivity tests');
      console.log('   → Set TELEGRAM_BOT_TOKEN to test bot connectivity');
    } else {
      try {
        const bot = new Bot(botToken);
        const botInfo = await bot.api.getMe();
        console.log(`✅ Bot connection successful: @${botInfo.username}`);
        
        // Test logs topics health
        console.log('\nTesting logs topics accessibility...');
        const healthCheck = await topicManager.checkLogsTopicsHealth(bot);
        
        console.log(`📊 Health check results: ${healthCheck.healthy}/${healthCheck.total} topics healthy`);
        
        if (healthCheck.problematic.length > 0) {
          console.log('\n❌ Problematic topics:');
          healthCheck.problematic.forEach(({ topic, error }) => {
            console.log(`   ${topic.group_title}${topic.topic_title ? ` / ${topic.topic_title}` : ''}: ${error}`);
          });
        }
        
      } catch (error) {
        console.error('❌ Bot connection failed:', error);
        console.log('   → Check TELEGRAM_BOT_TOKEN is valid');
        console.log('   → Ensure bot is not blocked by Telegram');
      }
    }
    console.log();

    // Step 4: Test message formatting
    console.log('📝 Step 4: Message Formatting Test');
    console.log('-'.repeat(30));
    
    try {
      const testHeadline = 'Test headline for diagnostic check';
      const testUrl = 'https://twitter.com/test/status/**********';
      
      const logsMessage = await aiSummarizer.generateLogsReplyText(testHeadline, testUrl);
      console.log('✅ Message formatting successful');
      console.log(`   Sample: "${logsMessage}"`);
      
      // Validate format
      const parts = logsMessage.split(' | ');
      if (parts.length === 3) {
        console.log('✅ Format structure correct (3 parts: date | headline | link)');
        
        // Check date format
        const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
        if (dateRegex.test(parts[0])) {
          console.log('✅ Date format correct (DD/MM/YYYY)');
        } else {
          console.error('❌ Date format incorrect');
        }
        
        // Check link format
        if (parts[2].includes('[Link]')) {
          console.log('✅ Link format correct (anchor link with markdown)');
        } else {
          console.error('❌ Link format incorrect');
        }
      } else {
        console.error('❌ Format structure incorrect - expected 3 parts');
      }
      
    } catch (error) {
      console.error('❌ Message formatting failed:', error);
      console.log('   → Check AI summarizer configuration');
      console.log('   → Verify OPENROUTER_API_KEY is set');
    }
    console.log();

    // Step 5: Environment check
    console.log('🔧 Step 5: Environment Configuration');
    console.log('-'.repeat(30));
    
    const requiredEnvVars = [
      'TELEGRAM_BOT_TOKEN',
      'OPENROUTER_API_KEY',
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY'
    ];
    
    requiredEnvVars.forEach(envVar => {
      if (process.env[envVar]) {
        console.log(`✅ ${envVar}: Set`);
      } else {
        console.error(`❌ ${envVar}: Missing`);
      }
    });
    console.log();

    // Step 6: Database schema check
    console.log('🗄️  Step 6: Database Schema Check');
    console.log('-'.repeat(30));
    
    try {
      // Test if error tracking table exists by trying to get recent errors
      const recentErrors = await logsErrorTracker.getRecentErrors(1);
      console.log('✅ Error tracking table accessible');
      
      if (recentErrors.length > 0) {
        console.log(`   Found ${recentErrors.length} recent unresolved error(s)`);
      } else {
        console.log('   No recent unresolved errors');
      }
      
    } catch (error) {
      console.error('❌ Error tracking table issue:', error);
      console.log('   → Run the logs_distribution_errors_migration.sql');
      console.log('   → Check database connection');
    }
    console.log();

    // Step 7: Recommendations
    console.log('💡 Step 7: Recommendations');
    console.log('-'.repeat(30));
    
    console.log('To improve logs distribution reliability:');
    console.log('1. Monitor logs regularly using the error tracking system');
    console.log('2. Set up periodic health checks for logs topics');
    console.log('3. Configure alerting for critical failures (all topics failing)');
    console.log('4. Regularly clean up inactive topics from the database');
    console.log('5. Monitor bot permissions in telegram groups');
    console.log();
    
    console.log('For debugging specific issues:');
    console.log('- Check server logs for detailed error messages');
    console.log('- Use test scripts to verify individual components');
    console.log('- Monitor Telegram API rate limits');
    console.log('- Verify markdown formatting in messages');
    console.log();

    console.log('🎉 Diagnostic complete!');
    
  } catch (error) {
    console.error('💥 Diagnostic failed:', error);
    console.log('\nTroubleshooting steps:');
    console.log('1. Check environment variables are set correctly');
    console.log('2. Verify database connection');
    console.log('3. Ensure required migrations have been run');
    console.log('4. Check network connectivity');
  }
}

// Run the diagnostic
if (require.main === module) {
  diagnoseLogs().catch(console.error);
}