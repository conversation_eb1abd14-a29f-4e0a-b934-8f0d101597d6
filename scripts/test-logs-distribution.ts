#!/usr/bin/env bun

/**
 * Manual test script for logs distribution
 * Simulates the logs distribution process without processing a real tweet
 */

import { Bo<PERSON> } from 'grammy';
import { topicManager } from '../lib/telegram/topic-manager';
import { aiSummarizer } from '../lib/ai/summarizer';
import { logsErrorTracker } from '../lib/telegram/logs-error-tracker';

async function testLogsDistribution() {
  console.log('🧪 Testing Logs Distribution Manually');
  console.log('=' .repeat(40));
  console.log();

  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  if (!botToken) {
    console.error('❌ TELEGRAM_BOT_TOKEN not found in environment');
    process.exit(1);
  }

  try {
    // Initialize bot
    const bot = new Bot(botToken);
    
    // Test data
    const testHeadline = 'Manual Test: Distribution System Check';
    const testUrl = 'https://twitter.com/test/status/9999999999';
    
    console.log('📝 Test Parameters:');
    console.log(`   Headline: "${testHeadline}"`);
    console.log(`   URL: ${testUrl}`);
    console.log();

    // Step 1: Get logs topics
    console.log('1️⃣  Fetching logs topics...');
    const logsTopics = await topicManager.getLogsTopics();
    
    if (logsTopics.length === 0) {
      console.error('❌ No active logs topics found');
      console.log('   Use /settype logs command in Telegram to configure topics');
      return;
    }
    
    console.log(`✅ Found ${logsTopics.length} logs topics:`);
    logsTopics.forEach((topic, index) => {
      console.log(`   ${index + 1}. ${topic.group_title}${topic.topic_title ? ` / ${topic.topic_title}` : ''} (${topic.group_id}/${topic.topic_id || 'main'})`);
    });
    console.log();

    // Step 2: Generate message
    console.log('2️⃣  Generating logs message...');
    const logsText = await aiSummarizer.generateLogsReplyText(testHeadline, testUrl);
    console.log(`✅ Generated: "${logsText}"`);
    console.log();

    // Step 3: Health check first
    console.log('3️⃣  Performing health check...');
    const healthCheck = await topicManager.checkLogsTopicsHealth(bot);
    console.log(`📊 Health: ${healthCheck.healthy}/${healthCheck.total} topics accessible`);
    
    if (healthCheck.problematic.length > 0) {
      console.warn('⚠️  Problematic topics detected:');
      healthCheck.problematic.forEach(({ topic, error }) => {
        console.warn(`   ${topic.group_title}${topic.topic_title ? ` / ${topic.topic_title}` : ''}: ${error}`);
      });
      console.log();
    }

    // Step 4: Ask for confirmation
    console.log('4️⃣  Ready to send test message to all healthy logs topics');
    console.log(`   This will send "${logsText}" to ${healthCheck.healthy} topics`);
    console.log();
    
    // Simple confirmation (in a real CLI you might use readline)
    const shouldSend = process.argv.includes('--send');
    
    if (!shouldSend) {
      console.log('ℹ️  To actually send the test message, run with --send flag:');
      console.log('   bun run scripts/test-logs-distribution.ts --send');
      console.log();
      console.log('🔍 Dry run complete - no messages sent');
      return;
    }

    // Step 5: Send to logs topics
    console.log('5️⃣  Sending test messages...');
    let successCount = 0;
    let failureCount = 0;
    
    for (let i = 0; i < logsTopics.length; i++) {
      const topic = logsTopics[i];
      const topicLabel = `${topic.group_title}${topic.topic_title ? ` / ${topic.topic_title}` : ''}`;
      
      try {
        console.log(`[${i + 1}/${logsTopics.length}] Sending to: ${topicLabel}`);
        
        await bot.api.sendMessage(topic.group_id, logsText, {
          parse_mode: 'Markdown',
          message_thread_id: topic.topic_id || undefined
        });
        
        successCount++;
        console.log(`✅ Success: ${topicLabel}`);
        
      } catch (error) {
        failureCount++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`❌ Failed: ${topicLabel} - ${errorMessage}`);
        
        // Record error for tracking
        await logsErrorTracker.recordError({
          tweet_url: testUrl,
          headline: testHeadline,
          logs_message: logsText,
          group_id: topic.group_id,
          topic_id: topic.topic_id || undefined,
          group_title: topic.group_title || undefined,
          topic_title: topic.topic_title || undefined,
          error_type: '',
          error_message: errorMessage,
          retry_count: 0
        });
      }
    }

    console.log();
    console.log('📊 Distribution Results:');
    console.log(`   ✅ Successful: ${successCount}/${logsTopics.length}`);
    console.log(`   ❌ Failed: ${failureCount}/${logsTopics.length}`);
    
    if (failureCount > 0) {
      console.log('\n🔍 Failed topics have been logged for analysis');
      console.log('   Check admin dashboard or run diagnose-logs-issues.ts for details');
    }

    console.log();
    console.log('🎉 Test distribution complete!');

  } catch (error) {
    console.error('💥 Test failed:', error);
    
    if (error instanceof Error && error.message.includes('401 Unauthorized')) {
      console.error('   → Bot token may be invalid');
    } else if (error instanceof Error && error.message.includes('network')) {
      console.error('   → Network connectivity issue');
    }
  }
}

// Run the test
if (require.main === module) {
  testLogsDistribution().catch(console.error);
}