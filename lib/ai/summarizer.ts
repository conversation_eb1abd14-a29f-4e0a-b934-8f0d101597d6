import { generateText } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { Tweet } from '@/lib/twitter';

export type SummaryResult = {
  headline: string;
  summary: string;
};

export class AISummarizer {
  private model;
  private client;

  constructor() {
    // Initialize OpenRouter client using OpenAI-compatible interface
    this.client = createOpenAI({
      apiKey: process.env.OPENROUTER_API_KEY!,
      baseURL: 'https://openrouter.ai/api/v1',
    });
    
    // Get model from environment variable with fallback
    const modelName = process.env.AI_MODEL || 'google/gemini-2.0-flash-exp';
    this.model = this.client(modelName);
    
    console.log(`[AISummarizer] Using OpenRouter model: ${modelName}`);
  }

  async summarizeContext(tweets: Tweet[]): Promise<SummaryResult> {
    if (tweets.length === 0) {
      throw new Error('No tweets provided for summarization');
    }

    // Sort tweets by creation date to maintain chronological order
    const sortedTweets = [...tweets].sort((a, b) => 
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );

    // Format tweets for the AI prompt
    const tweetContext = sortedTweets.map((tweet, index) => {
      const metrics = tweet.public_metrics;
      return `${index + 1}. @${tweet.author.username} (${tweet.author.name}):
"${tweet.text}"
[${metrics.like_count} likes, ${metrics.retweet_count} retweets, ${metrics.reply_count} replies]
Posted: ${new Date(tweet.created_at).toLocaleString()}`;
    }).join('\n\n');

    // Determine if this is a single tweet or a thread
    const isThread = tweets.length > 1;
    
    const prompt = `Analyze this Twitter/X ${isThread ? 'thread' : 'tweet'} and provide:
1. A concise, fluff-free headline (max 10 words)
2. A single paragraph summary (max 3 sentences) that captures the essence

Tweet content:
${tweetContext}

IMPORTANT FORMATTING RULES:
- Use plain text only, no markdown, no special formatting
- Don't use asterisks, underscores, brackets, or other special characters
- Write naturally as if speaking, not as formatted text
- Be extremely concise, no introduction, no fluff

Format your response as:
HEADLINE: [your headline here]
SUMMARY: [your paragraph here]`;

    try {
      const result = await generateText({
        model: this.model,
        prompt,
        maxTokens: 500,
        temperature: 0.3,
      });

      // Parse the result to extract headline and summary
      const responseText = result.text;
      
      // Extract headline and summary using regex
      const headlineMatch = responseText.match(/HEADLINE:\s*(.+)/);
      const summaryMatch = responseText.match(/SUMMARY:\s*([\s\S]+?)(?=\n|$)/);
      
      const headline = headlineMatch ? headlineMatch[1].trim() : 'Tweet Summary';
      const summary = summaryMatch ? summaryMatch[1].trim() : responseText.trim();

      return {
        headline,
        summary
      };
    } catch (error) {
      console.error('AI summarization error:', error);
      throw new Error('Failed to generate summary');
    }
  }


  private determineThreadType(tweets: Tweet[]): string {
    if (tweets.length === 1) {
      const tweet = tweets[0];
      if (tweet.referenced_tweets?.some(ref => ref.type === 'replied_to')) {
        return 'a single tweet that is part of a conversation or reply to another tweet';
      }
      return 'a standalone tweet';
    }

    // Check if all tweets are from the same author (thread)
    const authors = new Set(tweets.map(t => t.author.id));
    if (authors.size === 1) {
      return `a ${tweets.length}-tweet thread by @${tweets[0].author.username}`;
    }

    // Mixed authors - conversation
    return `a ${tweets.length}-tweet conversation between ${authors.size} participants`;
  }

  async generateReplyText(originalTweet: Tweet, summary: SummaryResult, tweets: Tweet[], tweetUrl: string): Promise<string> {
    return `**${summary.headline}**\n\n${summary.summary}\n\nOriginal Tweet: [Link](${tweetUrl})`;
  }

  async generateLogsReplyText(headline: string, tweetUrl: string): Promise<string> {
    const dateStr = this.formatDateDDMMYYYY(new Date());

    // Make headline more concise - limit to 50 characters
    let conciseHeadline = headline;
    if (headline.length > 50) {
      conciseHeadline = headline.substring(0, 47) + '...';
    }

    // Simple format: DD/MM/YYYY | Headline | Link
    return `${dateStr} | ${conciseHeadline} | [Link](${tweetUrl})`;
  }

  private formatDateDDMMYYYY(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }
}

export const aiSummarizer = new AISummarizer();