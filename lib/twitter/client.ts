import { createBotClient } from "@/lib/supabase/bot-client";

export interface Tweet {
  id: string;
  text: string;
  author: {
    id: string;
    username: string;
    name: string;
  };
  created_at: string;
  public_metrics: {
    retweet_count: number;
    like_count: number;
    reply_count: number;
    quote_count: number;
  };
  referenced_tweets?: Array<{
    type: 'replied_to' | 'quoted' | 'retweeted';
    id: string;
  }>;
  in_reply_to_user_id?: string;
}

interface RawTweet {
  id: string;
  text: string;
  author_id?: string;
  created_at: string;
  public_metrics?: {
    retweet_count: number;
    like_count: number;
    reply_count: number;
    quote_count: number;
  };
  referenced_tweets?: Array<{
    type: 'replied_to' | 'quoted' | 'retweeted';
    id: string;
  }>;
  in_reply_to_user_id?: string;
  username?: string;
  name?: string;
  user?: {
    id: string;
    username: string;
    name: string;
  };
}

export interface TwitterApiResponse {
  data: RawTweet[];
  includes?: {
    users?: Array<{
      id: string;
      username: string;
      name: string;
    }>;
    tweets?: RawTweet[];
  };
}

class TwitterApiError extends Error {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'TwitterApiError';
  }
}

export class TwitterClient {
  private apiKey: string;
  private baseUrl = 'https://api.twitterapi.io';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async fetchTweets(tweetIds: string[]): Promise<Tweet[]> {
    const params = new URLSearchParams({
      tweet_ids: tweetIds.join(',')
    });

    const response = await fetch(`${this.baseUrl}/twitter/tweets?${params}`, {
      headers: {
        'X-API-Key': this.apiKey,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new TwitterApiError(`Twitter API error: ${response.status}`, response.status);
    }

    const data = await response.json();
    
    if (!data.tweets) {
      throw new TwitterApiError('No tweet data returned');
    }

    // TwitterAPI.io returns tweets with author information already embedded
    const tweets: Tweet[] = data.tweets.map((tweet: RawTweet) => ({
      id: tweet.id,
      text: tweet.text,
      author: {
        id: tweet.author_id || tweet.user?.id || '',
        username: tweet.username || tweet.user?.username || 'unknown',
        name: tweet.name || tweet.user?.name || 'Unknown User'
      },
      created_at: tweet.created_at,
      public_metrics: tweet.public_metrics || {
        retweet_count: 0,
        like_count: 0,
        reply_count: 0,
        quote_count: 0
      },
      referenced_tweets: tweet.referenced_tweets,
      in_reply_to_user_id: tweet.in_reply_to_user_id
    }));

    return tweets;
  }

  async fetchTweetContext(tweetId: string): Promise<Tweet[]> {
    try {
      // Use TwitterAPI.io's thread context endpoint
      const params = new URLSearchParams({
        tweetId: tweetId
      });

      const response = await fetch(`${this.baseUrl}/twitter/tweet/thread_context?${params}`, {
        headers: {
          'X-API-Key': this.apiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new TwitterApiError(`Twitter API error: ${response.status}`, response.status);
      }

      const data = await response.json();
      
      if (!data.tweets) {
        // Fallback to single tweet if no thread context
        return await this.fetchTweets([tweetId]);
      }

      // Process the thread tweets from TwitterAPI.io
      const allTweets: Tweet[] = data.tweets.map((tweet: RawTweet) => ({
        id: tweet.id,
        text: tweet.text,
        author: {
          id: tweet.author_id || tweet.user?.id || '',
          username: tweet.username || tweet.user?.username || 'unknown',
          name: tweet.name || tweet.user?.name || 'Unknown User'
        },
        created_at: tweet.created_at,
        public_metrics: tweet.public_metrics || {
          retweet_count: 0,
          like_count: 0,
          reply_count: 0,
          quote_count: 0
        },
        referenced_tweets: tweet.referenced_tweets,
        in_reply_to_user_id: tweet.in_reply_to_user_id
      }));

      // Find the original tweet to identify the author
      const originalTweet = allTweets.find(t => t.id === tweetId);
      if (!originalTweet) {
        // If we can't find the original tweet, return all tweets as fallback
        allTweets.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        return allTweets;
      }

      // Filter to only include tweets from the original author
      const authorTweets = allTweets.filter(tweet => tweet.author.id === originalTweet.author.id);

      // Sort tweets chronologically (oldest first)
      authorTweets.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      
      console.log(`[TwitterClient] Filtered thread: ${authorTweets.length} tweets from @${originalTweet.author.username} (out of ${allTweets.length} total)`);
      
      return authorTweets;

    } catch (error) {
      console.warn(`Failed to fetch tweet context for ${tweetId}:`, error);
      // Fallback to single tweet
      try {
        return await this.fetchTweets([tweetId]);
      } catch (fallbackError) {
        console.error(`Failed to fetch single tweet ${tweetId}:`, fallbackError);
        return [];
      }
    }
  }


  async getCachedSummary(tweetId: string): Promise<{ text: string; summary: string; context: Tweet[] } | null> {
    const supabase = createBotClient();
    
    const { data, error } = await supabase
      .from('buddyintels_tweet_summaries')
      .select('*')
      .eq('tweet_id', tweetId)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !data) return null;

    return {
      text: data.tweet_text,
      summary: data.context_summary,
      context: data.context_tweets
    };
  }

  async cacheSummary(tweetId: string, tweetText: string, summary: string, context: Tweet[]): Promise<void> {
    const supabase = createBotClient();
    
    await supabase
      .from('buddyintels_tweet_summaries')
      .upsert({
        tweet_id: tweetId,
        tweet_text: tweetText,
        context_summary: summary,
        context_tweets: context,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      });
  }
}

export const twitterClient = new TwitterClient(process.env.TWITTER_API_KEY!);