import { createBotClient } from '@/lib/supabase/bot-client';

// TypeScript interfaces for type safety
interface ProcessedMessage {
  tweet_url: string;
  processed_at: string;
}


interface SummaryData {
  headline?: string;
  [key: string]: unknown;
}

interface RecapEntry {
  headline: string;
  date: string;
  url: string;
  tweetId: string;
}

export class RecapManager {
  private readonly MAX_RECAP_ITEMS = 50; // Limit for performance

  async getWeeklyRecap(): Promise<string> {
    try {
      const supabase = createBotClient();
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      // Fetch messages with limit for performance
      const { data: messages, error: messagesError } = await supabase
        .from('buddyintels_processed_messages')
        .select('tweet_url, processed_at')
        .gte('processed_at', oneWeekAgo.toISOString())
        .order('processed_at', { ascending: false })
        .limit(this.MAX_RECAP_ITEMS);

      if (messagesError) {
        console.error('Error fetching weekly recap messages:', messagesError);
        throw new Error(`Database error: ${messagesError.message}`);
      }

      if (!messages || messages.length === 0) {
        return 'No news in the last week.';
      }

      // Extract tweet IDs and fetch all summaries in a single query (fixes N+1 problem)
      const tweetIds = messages
        .map(m => this.extractTweetId(m.tweet_url))
        .filter(id => id !== ''); // Filter out invalid IDs

      if (tweetIds.length === 0) {
        return 'No valid tweets found in the last week.';
      }

      const { data: summaries, error: summariesError } = await supabase
        .from('buddyintels_tweet_summaries')
        .select('tweet_id, context_summary')
        .in('tweet_id', tweetIds);

      if (summariesError) {
        console.error('Error fetching tweet summaries:', summariesError);
        throw new Error(`Database error: ${summariesError.message}`);
      }

      // Create a map for O(1) lookup
      const summaryMap = new Map<string, string>();
      summaries?.forEach(summary => {
        summaryMap.set(summary.tweet_id, summary.context_summary);
      });

      // Process messages and build recap entries
      const recapEntries = this.buildRecapEntries(messages, summaryMap);

      if (recapEntries.length === 0) {
        return 'No summarized news found in the last week.';
      }

      return this.formatRecapMessage(recapEntries);
    } catch (error) {
      console.error('Error generating weekly recap:', error);
      throw error; // Re-throw for proper error handling in the bot
    }
  }

  private buildRecapEntries(
    messages: ProcessedMessage[],
    summaryMap: Map<string, string>
  ): RecapEntry[] {
    const entries: RecapEntry[] = [];

    for (const message of messages) {
      const tweetId = this.extractTweetId(message.tweet_url);
      if (!tweetId) continue;

      const summaryJson = summaryMap.get(tweetId);
      if (!summaryJson) continue;

      try {
        const summaryData = this.parseSummaryData(summaryJson);
        if (!summaryData.headline) {
          console.warn(`No headline found for tweet ${tweetId}`);
          continue;
        }

        const date = new Date(message.processed_at);
        const formattedDate = date.toLocaleDateString('en-GB', {
          day: '2-digit',
          month: '2-digit'
        });

        entries.push({
          headline: summaryData.headline,
          date: formattedDate,
          url: message.tweet_url,
          tweetId
        });
      } catch (error) {
        console.error(`Error processing summary for tweet ${tweetId}:`, error);
        // Continue processing other entries instead of failing completely
        continue;
      }
    }

    return entries;
  }

  private parseSummaryData(summaryJson: string): SummaryData {
    try {
      const parsed = JSON.parse(summaryJson);
      if (typeof parsed !== 'object' || parsed === null) {
        throw new Error('Invalid summary data structure');
      }
      return parsed as SummaryData;
    } catch (error) {
      throw new Error(`Failed to parse summary JSON: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private formatRecapMessage(entries: RecapEntry[]): string {
    let recapMessage = '**Weekly News Recap**\n\n';
    
    // Sort entries by date (newest first)
    entries.sort((a, b) => {
      const [dayA, monthA] = a.date.split('/').map(Number);
      const [dayB, monthB] = b.date.split('/').map(Number);
      return monthB - monthA || dayB - dayA;
    });

    for (const entry of entries) {
      recapMessage += `- ${entry.headline} | ${entry.date} | ${entry.url}\n`;
    }

    recapMessage += `\n📊 Total news items: ${entries.length}`;
    
    return recapMessage;
  }

  private extractTweetId(url: string): string {
    if (!url || typeof url !== 'string') {
      return '';
    }
    
    const match = url.match(/status\/(\d+)/);
    return match ? match[1] : '';
  }
}

export const recapManager = new RecapManager();
