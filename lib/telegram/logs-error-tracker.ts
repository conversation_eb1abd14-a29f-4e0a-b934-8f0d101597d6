import { createBotClient } from '@/lib/supabase/bot-client';

export type LogsDistributionError = {
  tweet_url: string;
  headline: string;
  logs_message: string;
  group_id: number;
  topic_id?: number;
  group_title?: string;
  topic_title?: string;
  error_type: string;
  error_message: string;
  error_details?: Record<string, unknown>;
  retry_count?: number;
};

export type LogsErrorStats = {
  error_type: string;
  error_count: number;
  affected_topics: number;
  latest_occurrence: string;
};

export type ProblematicTopic = {
  group_id: number;
  topic_id?: number;
  group_title?: string;
  topic_title?: string;
  error_count: number;
  latest_error: string;
  most_common_error: string;
};

export class LogsErrorTracker {
  /**
   * Record a logs distribution error
   */
  async recordError(error: LogsDistributionError): Promise<void> {
    try {
      const supabase = createBotClient();
      
      const { error: insertError } = await supabase
        .from('buddyintels_logs_distribution_errors')
        .insert({
          tweet_url: error.tweet_url,
          headline: error.headline,
          logs_message: error.logs_message,
          group_id: error.group_id,
          topic_id: error.topic_id || null,
          group_title: error.group_title || null,
          topic_title: error.topic_title || null,
          error_type: this.categorizeError(error.error_message),
          error_message: error.error_message,
          error_details: error.error_details || null,
          retry_count: error.retry_count || 0
        });

      if (insertError) {
        console.error('[LogsErrorTracker] Failed to record error:', insertError);
      } else {
        console.log(`[LogsErrorTracker] Recorded error for group ${error.group_id}/${error.topic_id || 'main'}: ${this.categorizeError(error.error_message)}`);
      }
    } catch (err) {
      console.error('[LogsErrorTracker] Exception while recording error:', err);
    }
  }

  /**
   * Get error statistics for the past N days
   */
  async getErrorStats(daysBack: number = 7): Promise<LogsErrorStats[]> {
    try {
      const supabase = createBotClient();
      
      const { data, error } = await supabase
        .rpc('get_logs_distribution_stats', { days_back: daysBack });

      if (error) {
        console.error('[LogsErrorTracker] Failed to get error stats:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('[LogsErrorTracker] Exception while getting error stats:', err);
      return [];
    }
  }

  /**
   * Get list of problematic logs topics
   */
  async getProblematicTopics(daysBack: number = 7): Promise<ProblematicTopic[]> {
    try {
      const supabase = createBotClient();
      
      const { data, error } = await supabase
        .rpc('get_problematic_logs_topics', { days_back: daysBack });

      if (error) {
        console.error('[LogsErrorTracker] Failed to get problematic topics:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('[LogsErrorTracker] Exception while getting problematic topics:', err);
      return [];
    }
  }

  /**
   * Mark errors as resolved for a specific topic
   */
  async markTopicErrorsResolved(groupId: number, topicId?: number): Promise<void> {
    try {
      const supabase = createBotClient();
      
      let query = supabase
        .from('buddyintels_logs_distribution_errors')
        .update({ resolved: true })
        .eq('group_id', groupId)
        .eq('resolved', false);

      if (topicId) {
        query = query.eq('topic_id', topicId);
      } else {
        query = query.is('topic_id', null);
      }

      const { error } = await query;

      if (error) {
        console.error('[LogsErrorTracker] Failed to mark errors as resolved:', error);
      } else {
        console.log(`[LogsErrorTracker] Marked errors as resolved for group ${groupId}/${topicId || 'main'}`);
      }
    } catch (err) {
      console.error('[LogsErrorTracker] Exception while marking errors as resolved:', err);
    }
  }

  /**
   * Categorize error messages into types for better tracking
   */
  private categorizeError(errorMessage: string): string {
    const message = errorMessage.toLowerCase();
    
    if (message.includes('chat not found') || message.includes('group not found')) {
      return 'bot_removed';
    }
    
    if (message.includes('topic not found') || message.includes('thread not found')) {
      return 'topic_deleted';
    }
    
    if (message.includes('too many requests') || message.includes('rate limit')) {
      return 'rate_limited';
    }
    
    if (message.includes('parse_mode') || message.includes('markdown')) {
      return 'markdown_error';
    }
    
    if (message.includes('timeout') || message.includes('network')) {
      return 'network_error';
    }
    
    if (message.includes('forbidden') || message.includes('access')) {
      return 'access_denied';
    }
    
    if (message.includes('message too long')) {
      return 'message_too_long';
    }
    
    return 'unknown_error';
  }

  /**
   * Get recent errors for debugging
   */
  async getRecentErrors(limit: number = 50): Promise<Record<string, unknown>[]> {
    try {
      const supabase = createBotClient();
      
      const { data, error } = await supabase
        .from('buddyintels_logs_distribution_errors')
        .select('*')
        .eq('resolved', false)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('[LogsErrorTracker] Failed to get recent errors:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('[LogsErrorTracker] Exception while getting recent errors:', err);
      return [];
    }
  }
}

// Export singleton instance
export const logsErrorTracker = new LogsErrorTracker();